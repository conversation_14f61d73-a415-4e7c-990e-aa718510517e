<template>
  <el-dialog
    title="简报信息配置"
    class="dialog-scenc"
    width="800"
    :modal-append-to-body="false"
    @close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    destroy-on-close
    v-model="visible"
  >
    <div class="config-container">
      <!-- 简报配置表单 -->
      <el-form ref="configForm" :model="formData" :rules="formRules" label-width="120px">
        <!-- 省报/地市报切换 -->
        <el-form-item>
          <el-radio-group v-model="formData.briefingType">
            <el-radio label="省报">省报</el-radio>
            <el-radio label="地市报">地市报</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="简报命名：" prop="briefingName">
          <span>中国铁塔</span>
          <el-input
            v-model="formData.briefingName"
            placeholder="请输入内容"
            style="width: 200px; margin: 0 10px;"
          />
          <span>应急保障信息推送（*分公司*年*月*日*时首报）</span>
        </el-form-item>

        <el-form-item label="应急原因：" prop="reason">
          <el-input
            v-model="formData.reason"
            placeholder="请输入内容"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="告警统计时间：" prop="alarmStartTime">
          <el-date-picker
            v-model="formData.alarmStartTime"
            type="datetime"
            placeholder="请选择开始时间"
            style="width: 200px; margin-right: 10px;"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="alarmStartTimeDisabledDate"
          />
          <el-date-picker
            v-model="formData.alarmEndTime"
            type="datetime"
            placeholder="请选择结束时间"
            style="width: 200px;"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="alarmEndTimeDisabledDate"
          />
        </el-form-item>

        <el-form-item label="告警内容展示：">
          <div>
            <span style="color: #fff; margin-right: 20px;">故障情况</span>
            <el-checkbox-group v-model="formData.alarmContent">
              <el-checkbox value="powerFailure">停电</el-checkbox>
              <el-checkbox value="undervoltage">欠压</el-checkbox>
              <el-checkbox value="withdraw">退服</el-checkbox>
              <el-checkbox value="offline">离线</el-checkbox>
              <el-checkbox value="suspectedWithdraw">疑似退服</el-checkbox>
              <el-checkbox value="temperature">温度过高</el-checkbox>
              <el-checkbox value="smoke">烟雾/火灾</el-checkbox>
              <el-checkbox value="water">水浸</el-checkbox>
            </el-checkbox-group>
          </div>
          <div style="margin-top: 20px;">
            <span style="color: #fff; margin-right: 20px;">保障力量投入</span>
            <el-checkbox-group v-model="formData.forceContent">
              <el-checkbox value="staff">保障人员</el-checkbox>
              <el-checkbox value="car">应急车辆</el-checkbox>
              <el-checkbox value="oilEngine">发电油机</el-checkbox>
              <el-checkbox value="satellitePhone">卫星电话</el-checkbox>
              <el-checkbox value="uav">无人机</el-checkbox>
              <el-checkbox value="other">其他</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>

        <el-form-item label="地市应急上报：">
          <el-button type="primary" @click="handleDownloadTemplate" style="margin-right: 10px;">下载模板</el-button>
          <el-button type="success" @click="handleImport" :loading="importLoading">批量导入</el-button>
          <input
            ref="fileInput"
            type="file"
            accept=".xlsx,.xls"
            style="display: none;"
            @change="handleFileChange"
          />
        </el-form-item>

        <el-form-item label="简报展示时间：" prop="displayStartTime">
          <el-date-picker
            v-model="formData.displayStartTime"
            type="datetime"
            placeholder="请选择开始时间"
            style="width: 200px; margin-right: 10px;"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="displayStartTimeDisabledDate"
          />
          <el-date-picker
            v-model="formData.displayEndTime"
            type="datetime"
            placeholder="请选择结束时间"
            style="width: 200px;"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="displayEndTimeDisabledDate"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleSave" type="primary" :loading="saveLoading">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { addBriefingInfo, downloadImportErrorData, exportGuaranteeInfoTemplate, importGuaranteeInfo } from "@/api/sceneView/index";
export default {
  name: "reportCfgTable",
  data() {
    return {
      visible: false,
      formData: {
        briefingType: "省报", // 简报类型
        briefingName: "", // 简报命名
        reason: "", // 应急原因
        alarmStartTime: null, // 告警统计开始时间
        alarmEndTime: null, // 告警统计结束时间
        alarmContent: [], // 告警内容展示
        forceContent: [], // 保障力量投入展示
        displayStartTime: null, // 简报展示开始时间
        displayEndTime: null, // 简报展示结束时间
      },
      saveLoading: false,
      importLoading: false,
      formRules: {
        briefingName: [
          { required: true, message: '请输入简报命名', trigger: 'blur' }
        ],
        reason: [
          { required: true, message: '请输入应急原因', trigger: 'blur' }
        ],
        alarmStartTime: [
          { required: true, message: '请选择告警统计开始时间', trigger: 'change' }
        ],
        displayStartTime: [
          { required: true, message: '请选择简报展示开始时间', trigger: 'change' }
        ]
      }
    };
  },
  methods: {
    handleOpen() {},
    initForm() {
      this.resetForm();
      this.visible = true;
      // 重置表单验证状态
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.clearValidate();
        }
      });
    },
    resetForm() {
      this.formData = {
        briefingType: "省报",
        briefingName: "",
        reason: "",
        alarmStartTime: null,
        alarmEndTime: null,
        alarmContent: [],
        forceContent: [],
        displayStartTime: null,
        displayEndTime: null,
      };
    },
    async handleSave() {
      // 表单验证
      try {
        await this.$refs.configForm.validate();
      } catch (error) {
        this.$message.error('请完善必填信息');
        return;
      }

      this.saveLoading = true;
      try {
        // 构建告警内容字符串
        const alarmContentStr = this.buildAlarmContentString();

        const data = {
          briefingType: this.formData.briefingType,
          name: this.formData.briefingName,
          reason: this.formData.reason,
          statisticsStartTime: this.formData.alarmStartTime,
          statisticsEndTime: this.formData.alarmEndTime,
          alarmContent: alarmContentStr,
          displayStartTime: this.formData.displayStartTime,
          displayEndTime: this.formData.displayEndTime,
        };

        await addBriefingInfo(data);
        this.$message.success('保存成功');
        this.handleClose();
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败');
      }
      this.saveLoading = false;
    },
    buildAlarmContentString() {
      // 构建符合后端要求的alarmContent字符串格式
      const alarmMapping = {
        powerFailure: '停电',
        undervoltage: '欠压',
        withdraw: '退服',
        offline: '离线',
        suspectedWithdraw: '疑似退服',
        temperature: '温度过高',
        smoke: '烟雾/火灾',
        water: '水浸'
      };

      const forceMapping = {
        staff: '保障人员',
        car: '应急车辆',
        oilEngine: '发电油机',
        satellitePhone: '卫星电话',
        uav: '无人机',
        other: '其他'
      };

      const result = [
        {"fieldName": "city", "titleName": "地市"},
        {"fieldName": "county", "titleName": "区县"}
      ];

      // 添加故障情况
      if (this.formData.alarmContent.length > 0) {
        const faultChildren = this.formData.alarmContent.map(fieldName => ({
          "titleName": alarmMapping[fieldName],
          "fieldName": fieldName
        }));

        result.push({
          "fieldName": "faultsSituation",
          "titleName": "故障情况",
          "Children": faultChildren
        });
      }

      // 添加保障情况
      if (this.formData.forceContent.length > 0) {
        const ensureChildren = this.formData.forceContent.map(fieldName => ({
          "titleName": forceMapping[fieldName],
          "fieldName": fieldName
        }));

        result.push({
          "fieldName": "ensureSituation",
          "titleName": "保障情况",
          "Children": ensureChildren
        });
      }

      return JSON.stringify(result);
    },
    handleCancel() {
      this.handleClose();
    },
    async handleDownloadTemplate() {
      try {
        const blob = await exportGuaranteeInfoTemplate();
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.setAttribute('download', '地市应急上报信息模板.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(link.href);
      } catch (error) {
        console.error('下载模板失败:', error);
        this.$message.error('下载模板失败');
      }
    },
    handleImport() {
      this.$refs.fileInput.click();
    },
    async handleFileChange(event) {
      const file = event.target.files[0];
      if (!file) return;

      this.importLoading = true;
      try {
        const formData = new FormData();
        formData.append('name', this.formData.briefingType);
        formData.append('file', file);

        const response = await importGuaranteeInfo(formData);
        const result = response.result;

        // 根据导入结果显示不同的消息
        if (result.errorNum > 0) {
          this.$message.warning(`导入完成：成功${result.successNum}条，失败${result.errorNum}条。${result.msg || ''}`);
          if (result.errorFileUrl) {
            this.$message.info('可下载异常文件查看失败原因');
            const blob = await downloadImportErrorData({ filePath: result.errorFileUrl })
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.setAttribute('download', '导入错误数据.xlsx');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(link.href);
          }
        } else {
          this.$message.success(`导入成功：共导入${result.successNum}条数据`);
        }

        // 清空文件输入
        this.$refs.fileInput.value = '';
      } catch (error) {
        console.error('导入失败:', error);
        this.$message.error('导入失败');
      }
      this.importLoading = false;
    },
    // 告警统计开始时间禁用日期方法
    alarmStartTimeDisabledDate(time) {
      if (this.formData.alarmEndTime) {
        return time.getTime() > new Date(this.formData.alarmEndTime).getTime();
      }
      return false;
    },
    // 告警统计结束时间禁用日期方法
    alarmEndTimeDisabledDate(time) {
      if (this.formData.alarmStartTime) {
        return time.getTime() < new Date(this.formData.alarmStartTime).getTime();
      }
      return false;
    },
    // 简报展示开始时间禁用日期方法
    displayStartTimeDisabledDate(time) {
      if (this.formData.displayEndTime) {
        return time.getTime() > new Date(this.formData.displayEndTime).getTime();
      }
      return false;
    },
    // 简报展示结束时间禁用日期方法
    displayEndTimeDisabledDate(time) {
      if (this.formData.displayStartTime) {
        return time.getTime() < new Date(this.formData.displayStartTime).getTime();
      }
      return false;
    },
    close() {
      this.visible = false;
    },
    handleClose() {
      this.close();
    },
  },
};
</script>

<style scoped lang="scss">
.config-container {
  padding: 20px;
  color: #fff;
}

:deep() .el-form-item__label {
  color: #fff;
}

:deep() .el-input__wrapper {
  background-color: transparent;
  width: 100%;
  &.is-focus {
    box-shadow: 0 0 0 1px #059ec0;
  }
}

:deep() .el-input__inner {
  color: #fff;
}

:deep() .el-radio__label {
  color: #fff;
}

:deep() .el-checkbox__label {
  color: #fff;
}

:deep() .el-button--primary {
  background: #059ec0;
  border-color: #059ec0;
}
</style>

<style lang="scss">
.dialog-scenc {
  background-color: #065e89;
  opacity: 0.9;
  .el-dialog__header {
    height: 55px;
    line-height: 55px;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #059ec0;
    overflow: hidden;
    padding: 0 20px !important;
    .el-dialog__title {
      color: #fff;
    }
  }
  .el-dialog__body {
    padding: 10px 20px 0 20px;
    .rowStyle {
      background: #084969 !important;
    }
    .pagination-container {
      background: transparent;
      border-left: none;
      border-right: none;
      display: flex;
      justify-content: flex-end;
      .el-pagination__total {
        color: #fff;
      }
      .el-pagination__jump {
        color: #fff;
      }
    }
    .table-box {
      .sticky {
        background-color: rgba(144, 147, 153, 0.5);
        .el-input__inner {
          color: #fff;
          background: linear-gradient(0deg, #385fb866, #2238690d);
          border: 2px solid #059ec0;
          color: #82bee9;
          margin-bottom: 6px;
        }
        .search-btn {
          background: #059ec0;
          color: #fff;
          border: 1px solid #059ec0;
          margin-top: -3px;
        }
      }
      .el-table--enable-row-hover
        .el-table__body
        tr:hover
        > td.el-table__cell {
        background-color: rgba(133, 210, 249, 0.23922) !important;
      }
      .el-table td.el-table__cell {
        border-bottom: 1px solid #059ec0;
        color: #fff;
        height: 45px;
        font-size: 16px;
      }
      .el-table tr {
        background-color: transparent;
        height: 45px;
      }
      .el-table {
        background-color: transparent;
        &::before {
          height: 0;
        }
      }
      .el-table th.el-table__cell {
        background: #084969;
        color: #d2e7ff;
        font-size: 17px;
        font-weight: 700;
        border-bottom: 1px solid #059ec0;
      }
      .el-table__empty-text {
        color: #fff;
      }
      .el-table--border {
        border: 1px solid #059ec0;
      }
      .el-table--border .el-table__cell {
        border-right: 1px solid #059ec0;
      }
      .el-table--border::after {
        width: 0;
      }
      .el-table__body-wrapper {
        .el-table__body {
          width: 100% !important;
        }
        &::-webkit-scrollbar {
          width: 6px;
          height: 12px;
        }
        &::-webkit-scrollbar-thumb {
          // border-radius: 6px;
          background: rgba(144, 147, 153, 0.5);
          border-radius: 0;
          -webkit-box-shadow: inset 0 0 5px #0003;
        }
        &::-webkit-scrollbar-track {
          background: transparent;
          -webkit-box-shadow: inset 0 0 5px #0003;
          border-radius: 0;
        }
      }
    }
  }
  .el-dialog__footer {
    border-top: 1px solid #059ec0;
  }
}
</style>
