<template>
  <el-dialog
    :title="title"
    class="dialog-scenc"
    width="80%"
    :modal-append-to-body="false"
    @close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    destroy-on-close
    v-model="visible"
  >
    <div class="table-box">
      <div class="filter-container">
        <el-button
          class="filter-item"
          style="margin-left: 8px"
          size="mini"
          type="primary"
          v-if="initCity == '全省' && city !== '全省'"
          @click="handleBack"
        >返回</el-button>
        <el-button
          class="filter-item"
          style="margin-left: 8px"
          size="mini"
          type="warning"
          @click="handleOpenReportCfgTable"
          >简报信息配置</el-button>
        <el-button
        class="filter-item search-btn"
        style="margin-left: 8px"
        size="mini"
        @click="handleExport"
        :loading="exportLoading"
        >导出</el-button>
      </div>
      <el-table
        ref="mainTable"
        height="685"
        border
        fit
        :data="tableList"
        v-loading="listLoading"
        style="width: 100%"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
      >
        <el-table-column
          :prop="city == '全省' ? 'city' : 'county'"
          :label="city == '全省' ? '地市' : '区县'"
          min-width="100px"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="故障情况"
          prop="faultSituation"
        >
          <!-- 动态告警情况列 -->
          <el-table-column
            v-for="alarm in alarmColumns"
            :key="alarm.prop"
            :label="alarm.label"
            :prop="alarm.prop"
            min-width="80px"
            align="center"
            show-overflow-tooltip
          />
        </el-table-column>
      <el-table-column
        label="保障情况"
        prop="guaranteeSituation"
      >
        <!-- 动态保障力量情况列 -->
        <el-table-column
          v-for="force in forceColumns"
          :key="force.prop"
          :label="force.label"
          :prop="force.prop"
          min-width="80px"
          align="center"
          show-overflow-tooltip
        />
      </el-table-column>
      </el-table>
    </div>
    <!-- 底部统计信息 -->
    <div  style="color: #fff; padding: 10px 0; font-size: 20px;">
      {{ summaryInfo || '(暂无统计信息)' }}
    </div>
  </el-dialog>
  <report-cfg-table ref="reportCfgTable" @close="handleCfgClose"></report-cfg-table>
</template>

<script>
import { exportGuaranteeInfo,  getGuaranteeInfoPage } from "@/api/sceneView/index";
import reportCfgTable from "./reportCfgTable";
import Pagination from "@/components/Pagination";
export default {
  name: "reportTable",
  data() {
    return {
      title: "",
      visible: false,
      city: '全省',
      total: 0,
      listLoading: false,
      tableList: [],
      briefingInfoName: "", // 简报信息名称
      accountOwnership: "", // 分公司
      alarmColumns: [], // 动态告警列
      forceColumns: [], // 动态保障力量列
      summaryInfo: "", // 底部统计信息
      listQuery: {
        // pageNum: 1,            // 页码
        // pageSize: 20,          // 每页数量
      },
      exportLoading: false
    };
  },
  created() {},
  computed: {},
  components: {
    reportCfgTable,
    Pagination
  },
  watch: {},
  methods: {
    handleBack() {
      this.city = '全省'
      this.getList()
    },
    handleRowClick(row) {
      if (this.city !== '全省') {
        return
      }
      this.city = row.city
      this.getList()
    },
    async handleExport(){
      this.exportLoading = true;
      try {
        const blob = await exportGuaranteeInfo({
          ...this.listQuery,
          pageSize: this.total
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.setAttribute('download',`${this.title}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(link.href);
      } catch (error) {
        console.error('下载附件失败:', error);
        this.$message.error('下载附件失败');
      }
      this.exportLoading = false;
    },
    handleOpen() {},
    async getList() {
      this.listLoading = true;
      try {
        const { result } = await getGuaranteeInfoPage(this.listQuery);
        this.tableList = result || [];
        const configItem = result?.[result.length - 1] || {};
        this.briefingInfoName = configItem?.briefingInfoName || "";
        this.accountOwnership = configItem?.accountOwnership || "";

        // 设置动态列
        this.setDynamicColumns(configItem?.alarmContent || []);

        // 设置底部统计信息
        this.setSummaryInfo(configItem?.remark);

        // 更新标题
        this.updateTitle();
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error('获取数据失败');
      }
      this.listLoading = false;
    },
    setDynamicColumns(alarmContent) {
      // 根据接口返回的alarmContent设置动态列
      this.alarmColumns = [];
      this.forceColumns = [];

      if (!alarmContent || typeof alarmContent !== 'string') {
        return;
      }

      try {
        const contentArray = JSON.parse(alarmContent);

        contentArray.forEach(item => {
          if (item.fieldName === 'faultsSituation' && item.Children) {
            // 故障情况列
            this.alarmColumns = item.Children.map(child => ({
              label: child.titleName,
              prop: child.fieldName
            }));
          } else if (item.fieldName === 'ensureSituation' && item.Children) {
            // 保障力量投入列
            this.forceColumns = item.Children.map(child => ({
              label: child.titleName,
              prop: child.fieldName
            }));
          }
        });
      } catch (error) {
        console.error('解析alarmContent失败:', error);
        // 如果解析失败，使用默认列
        this.alarmColumns = [
          { label: '停电', prop: 'powerFailure' },
          { label: '欠压', prop: 'undervoltage' },
          { label: '退服', prop: 'withdraw' },
          { label: '离线', prop: 'offline' },
          { label: '疑似退服', prop: 'suspectedWithdraw' },
          { label: '温度过高', prop: 'temperature' },
          { label: '烟雾/火灾', prop: 'smoke' },
          { label: '水浸', prop: 'water' }
        ];

        this.forceColumns = [
          { label: '保障人员', prop: 'staff' },
          { label: '应急车辆', prop: 'car' },
          { label: '发电油机', prop: 'oilEngine' },
          { label: '卫星电话', prop: 'satellitePhone' },
          { label: '无人机', prop: 'uav' },
          { label: '其他', prop: 'other' }
        ];
      }
    },
    setSummaryInfo(remark) {
      this.summaryInfo = remark;
    },
    updateTitle() {
      // 动态拼接标题
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      const hour = String(currentDate.getHours()).padStart(2, '0');
      const dateStr = `${year}年${month}月${day}日${hour}时`;

      this.title = `中国铁塔${this.briefingInfoName}应急保障信息推送（${this.accountOwnership}${dateStr}首报）`;
    },
    initForm(data, city) {
      this.city = city
      this.initCity = city
      this.listQuery = {
        // pageNum: 1,
        // pageSize: 20,
        ...data
      }
      this.visible = true;
      this.getList();
    },
    close() {
      this.listQuery = {
        // pageNum: 1,
        // pageSize: 20,
      }
      this.visible = false;
    },
    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 != 0 ? "rowStyle" : "";
    },
    handleCfgClose() {
      this.getList();
    },
    handleClose() {
      this.close();
    },
    handleOpenReportCfgTable() {
      this.$refs.reportCfgTable.initForm();
    }
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  text-align: right;
  padding: 8px 10px;
  background-color: #446f86;
  .filter-item {
    margin-bottom: 0;
  }
}
:deep() .el-input__wrapper {
  background-color: transparent;
  width: 100%;
  &.is-focus {
    box-shadow: 0 0 0 1px #059ec0;
  }
}
:deep() .el-input__inner {
  background-color: transparent;
  border: 0px solid #1296db;
  color: #82bee9;
}
.search-btn {
  background: #059ec0;
  color: #fff;
  border: 1px solid #059ec0;
  margin-top: -3px;
}
</style>

<style lang="scss">
.dialog-scenc {
  background-color: #065e89;
  opacity: 0.9;
  .el-table__header thead th {
    text-align: center !important;
  }
  .el-dialog__header {
    height: 55px;
    line-height: 55px;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #059ec0;
    overflow: hidden;
    padding: 0 20px !important;
    .el-dialog__title {
      color: #fff;
    }
  }
  .el-dialog__body {
    padding: 10px 20px 0 20px;
    .rowStyle {
      background: #084969 !important;
    }
    .pagination-container {
      background: transparent;
      border-left: none;
      border-right: none;
      display: flex;
      justify-content: flex-end;
      .el-pagination__total {
        color: #fff;
      }
      .el-pagination__jump {
        color: #fff;
      }
    }
    .table-box {
      .sticky {
        background-color: rgba(144, 147, 153, 0.5);
        .el-input__inner {
          background: linear-gradient(0deg, #385fb866, #2238690d);
          border: 2px solid #059ec0;
          color: #82bee9;
          margin-bottom: 6px;
        }
        .search-btn {
          background: #059ec0;
          color: #fff;
          border: 1px solid #059ec0;
          margin-top: -3px;
        }
      }
      .el-table--enable-row-hover
        .el-table__body
        tr:hover
        > td.el-table__cell {
        background-color: rgba(133, 210, 249, 0.23922) !important;
      }
      .el-table td.el-table__cell {
        border-bottom: 1px solid #059ec0;
        color: #fff;
        height: 45px;
        font-size: 16px;
      }
      .el-table tr {
        background-color: transparent;
        height: 45px;
      }
      .el-table {
        background-color: transparent;
        &::before {
          height: 0;
        }
      }
      .el-table th.el-table__cell {
        background: #084969;
        color: #d2e7ff;
        font-size: 17px;
        font-weight: 700;
        border-bottom: 1px solid #059ec0;
      }
      .el-table__empty-text {
        color: #fff;
      }
      .el-table--border {
        border: 1px solid #059ec0;
      }
      .el-table--border .el-table__cell {
        border-right: 1px solid #059ec0;
      }
      .el-table--border::after {
        width: 0;
      }
      .el-table__body-wrapper {
        .el-table__body {
          width: 100% !important;
        }
        &::-webkit-scrollbar {
          width: 6px;
          height: 12px;
        }
        &::-webkit-scrollbar-thumb {
          // border-radius: 6px;
          background: rgba(144, 147, 153, 0.5);
          border-radius: 0;
          -webkit-box-shadow: inset 0 0 5px #0003;
        }
        &::-webkit-scrollbar-track {
          background: transparent;
          -webkit-box-shadow: inset 0 0 5px #0003;
          border-radius: 0;
        }
      }
    }
  }
}
</style>
