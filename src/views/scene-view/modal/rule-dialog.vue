<template>
  <el-dialog
    :title="title"
    class="dialog-scenc"
    width="600px"
    :modal-append-to-body="false"
    @close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    destroy-on-close
    v-model="visible"
  >
    <div class="dialog-block-content">
      <div class="rule-config">
        <el-radio v-model="formData.cigType" label="次数">输入次数</el-radio>
        <el-radio v-model="formData.cigType" label="比例">输入比例</el-radio>
      </div>
      <div class="value-config">

        <div class="config-bar">
          <div class="color" :style="{ backgroundColor: colors[0].value }"></div>
          <div class="">
            当{{ formData.alarmType }}&lt;<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword1" size="mini" />{{ formData.cigType }}。
          </div>
        </div>

        <div class="config-bar">
          <div class="color" :style="{ backgroundColor: colors[1].value }"></div>
          <div class="">
            当{{ formData.alarmType }}≤<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword2" :min="formData.keyword1" size="mini" />{{ formData.cigType }};
            &lt;<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword3"
            :min="formData.cigType === '次数' ? formData.keyword2 + 1 : formData.keyword2" size="mini" />{{ formData.cigType }}。
          </div>
        </div>

        <div class="config-bar">
        <div class="color" :style="{ backgroundColor: colors[2].value }"></div>
        <div class="">
          当{{ formData.alarmType }}≤<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword4"
          :min="formData.cigType === '次数' ? formData.keyword3 : formData.keyword3" size="mini" />{{ formData.cigType }};
          &lt;<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword5"
          :min="formData.cigType === '次数' ? formData.keyword4 + 1 : formData.keyword4" size="mini" />{{ formData.cigType }}。
        </div>
      </div>
      <div class="config-bar">
        <div class="color" :style="{ backgroundColor: colors[3].value }"></div>
        <div class="">
          当{{ formData.alarmType }}≤<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword6"
          :min="formData.cigType === '次数' ? formData.keyword5 : formData.keyword5" size="mini" />{{ formData.cigType }};
          &lt;<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword7"
          :min="formData.cigType === '次数' ? formData.keyword6 + 1 : formData.keyword6" size="mini" />{{ formData.cigType }}。
        </div>
      </div>

        <div class="config-bar">
          <div class="color" :style="{ backgroundColor: colors[4].value }"></div>
          <div class="">
            当{{ formData.alarmType }}≥<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword8" :min="formData.keyword7" size="mini" />{{ formData.cigType }}。
          </div>
        </div>
      </div>
      <div class="btn-bar">
        <el-button class="submit-btn" size="small" @click="handleRuleClick">
          保存
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { updateAlarmProp } from "@/api/sceneView/index";
import { Colors } from "./mapChart.vue";

export default {
  name: "rule-dialog",
  data() {
    return {
      title: "",
      visible: false,
      loading: false,
      colors: Colors,
      formData: {},
    };
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    handleOpen() {},
    initForm(formData) {
      this.title = formData.alarmType + "规则配置";
      this.visible = true;
      this.formData = formData
    },
    async handleRuleClick () {
      this.loading = true;
      // for(let i = 1; i <= 8; i++) {
      //   this.formData[`keyword${i}`] = Number(this.formData[`keyword${i}`]);
      // }

      let keywords = ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5", "keyword6", "keyword7", "keyword8"];
      for (let i = 1; i < keywords.length; i++) {
        let currentKeyword = keywords[i];
        let previousKeyword = keywords[i - 1];

        if (this.formData[currentKeyword] < this.formData[previousKeyword]) {
          this.$message({
            message: '输入的值不合法',
            type: 'warning'
          });
          return false;
        }
      }
      await updateAlarmProp({
        ...this.formData
      })
      this.loading = false
      this.$message({
        message: '修改成功',
      })
      this.$emit("on-update", this.formData)
      this.close()
    },
    close() {
      this.formData = {};
      this.visible = false;
    },
    handleClose() {
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-block-content {
  font-size: 18px;
  padding: 10px 15px 20px;
}
.rule-config {
  margin-bottom: 8px;
}
.config-bar {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 8px;
}
.el-input-num {
  width: 120px;
  margin: 0px 6px;
}
.color {
  display: inline-block;
  width: 18px;
  height: 12px;
}
.btn-bar {
  text-align: center;
  margin: 0 auto;
}
</style>

<style lang="scss">
.dialog-scenc {
  color: #fff;
  background-color: #236e92;
  opacity: 0.9;
    .el-dialog__header {
      height: 55px;
      line-height: 55px;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #059ec0;
      overflow: hidden;
      padding: 0 20px !important;
      .el-dialog__title {
        color: #fff;
      }
    }
    .el-dialog__body {
      color: #fff;
      padding: 10px 20px 0 20px;
      // .el-radio,
      // .el-radio__label {
      //   color: #fff;
      // }
    }
    .el-radio,
    .el-radio__label {
      color: #fff;
    }
}
</style>
